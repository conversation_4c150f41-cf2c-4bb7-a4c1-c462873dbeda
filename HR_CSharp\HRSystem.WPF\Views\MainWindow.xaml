<Window x:Class="HRSystem.WPF.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="💼 برنامج إدارة الموظفين" Height="900" Width="1400"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="{materialDesign:MaterialDesignFont}"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        ShowInTaskbar="True"
        Visibility="Visible"
        Topmost="True"
        ShowActivated="True"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- استخدام الأنماط المحسنة من EnhancedStyles.xaml -->
    </Window.Resources>

    <Grid>
        <!-- المحتوى الرئيسي بدون عنوان علوي -->
        <Grid Margin="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الشريط الجانبي المحسن -->
            <Border Grid.Column="0" Background="#F5F5F5"
                    BorderBrush="#42A5F5" BorderThickness="0,0,2,0">
                <Border.Effect>
                    <DropShadowEffect Color="Gray" Direction="0" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Border.Effect>

                <StackPanel>
                    <!-- منطقة الشعار والعنوان المحسنة -->
                    <Border Margin="8" Height="140" Background="#1976D2" CornerRadius="8">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="8"/>
                        </Border.Effect>
                        <Grid>
                            <Grid.Effect>
                                <DropShadowEffect Color="#1976D2"
                                                Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="12"/>
                            </Grid.Effect>
                            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Domain" Width="48" Height="48"
                                                       Foreground="White" Margin="0,0,0,8"/>
                                <TextBlock Text="نظام إدارة الموظفين"
                                         HorizontalAlignment="Center"
                                         FontSize="20" FontWeight="Bold" Foreground="White"
                                         TextAlignment="Center"/>
                                <TextBlock Text="HR Management System"
                                         HorizontalAlignment="Center"
                                         FontSize="12" Foreground="#E3F2FD"
                                         TextAlignment="Center" Margin="0,4,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- منطقة الأزرار -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled"
                                Margin="15,20,15,20">
                        <StackPanel>
                            <!-- لوحة التحكم -->
                            <Button Background="#E3F2FD" BorderBrush="#1976D2" BorderThickness="1"
                                    Margin="8,4" Padding="20,16" Height="60"
                                    HorizontalAlignment="Stretch" HorizontalContentAlignment="Right"
                                    Command="{Binding ShowDashboardCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📊" FontSize="20" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="لوحة التحكم" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- الموظفين -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowEmployeeListCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountMultiple" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="الموظفين" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- الأقسام -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowDepartmentsCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Domain" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="الأقسام" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- المناصب -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowJobTitlesCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Briefcase" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="المناصب" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- الرواتب -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowSalaryViewCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashMultiple" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="الرواتب" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- المعاملات المالية -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowFinancialTransactionCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CurrencyUsd" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="المعاملات المالية" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- التقارير -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowReportsCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocument" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="التقارير" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- النسخ الاحتياطي -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowBackupCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Backup" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="النسخ الاحتياطي" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- تدقيق الأنشطة -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowAuditCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="History" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="تدقيق الأنشطة" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>

                            <!-- الإعدادات -->
                            <Button Style="{StaticResource EnhancedSidebarButtonStyle}"
                                    Command="{Binding ShowSettingsCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings" Style="{StaticResource EnhancedIconStyle}"/>
                                    <TextBlock Text="الإعدادات" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </ScrollViewer>
                </StackPanel>
            </Border>

            <!-- منطقة عرض المحتوى المحسنة -->
            <materialDesign:Card Grid.Column="1" Margin="16"
                               Style="{StaticResource EnhancedCardStyle}"
                               Background="{StaticResource SurfaceBrush}">
                <Grid Style="{StaticResource FadeInAnimation}">
                    <!-- لوحة التحكم -->
                    <ContentControl Content="{Binding DashboardViewModel}"
                                  Visibility="{Binding IsDashboardVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- الموظفين -->
                    <ContentControl Content="{Binding EmployeeListViewModel}"
                                  Visibility="{Binding IsEmployeeListVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- الأقسام -->
                    <ContentControl Content="{Binding DepartmentViewModel}"
                                  Visibility="{Binding IsDepartmentViewVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- المناصب -->
                    <ContentControl Content="{Binding JobTitlesViewModel}"
                                  Visibility="{Binding IsJobTitlesVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- الرواتب -->
                    <ContentControl Content="{Binding SalaryViewModel}"
                                  Visibility="{Binding IsSalaryViewVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- المعاملات المالية -->
                    <ContentControl Content="{Binding FinancialTransactionViewModel}"
                                  Visibility="{Binding IsFinancialTransactionVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- التقارير -->
                    <ContentControl Content="{Binding ReportsViewModel}"
                                  Visibility="{Binding IsReportsViewVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- النسخ الاحتياطي -->
                    <ContentControl Content="{Binding BackupViewModel}"
                                  Visibility="{Binding IsBackupVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- تدقيق الأنشطة -->
                    <ContentControl Content="{Binding AuditViewModel}"
                                  Visibility="{Binding IsAuditVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>

                    <!-- الإعدادات -->
                    <ContentControl Content="{Binding SettingsViewModel}"
                                  Visibility="{Binding IsSettingsVisible, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  Style="{StaticResource SlideInFromRightAnimation}"/>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Window>
