<Window x:Class="HRSystem.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:HRSystem.WPF"
        mc:Ignorable="d"
        Title="💼 نظام إدارة الموظفين" Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        ShowInTaskbar="True"
        Visibility="Visible"
        Topmost="True"
        ShowActivated="True"
        AllowsTransparency="False"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft">

    <Grid Background="#F5F5F5">
        <TextBlock Text="نظام إدارة الموظفين - تم تحميل النافذة بنجاح!"
                   FontSize="24" FontWeight="Bold"
                   HorizontalAlignment="Center" VerticalAlignment="Center"
                   Foreground="Blue"/>
    </Grid>
</Window>
