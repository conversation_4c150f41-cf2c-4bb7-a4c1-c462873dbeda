using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using HRSystem.Core.Interfaces;
using HRSystem.Core.Services;
using HRSystem.Data.Context;
using HRSystem.Data.Repositories;
using HRSystem.WPF.ViewModels;
using HRSystem.WPF.Views;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace HRSystem.WPF
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;

        public ServiceProvider ServiceProvider => _serviceProvider;

        public App()
        {
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "HRSystem.db");
            
            // DbContext
            services.AddDbContext<HRSystemContext>(options =>
                options.UseSqlite($"Data Source={dbPath}"));
            services.AddScoped<IHRSystemContext>(provider => provider.GetRequiredService<HRSystemContext>());

            // Repositories
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

            // Services
            services.AddScoped<EmployeeService>();
            services.AddScoped<SalaryService>();
            services.AddScoped<UserService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<NotificationService>();
            services.AddScoped<DataSeedingService>();
            services.AddScoped<IExportService, ExportService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<IAuditService, AuditService>();
            services.AddScoped<ISettingsService, SettingsService>();
            services.AddScoped<ISecurityService, SecurityService>();

            // ViewModels
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<EmployeeListViewModel>();

            services.AddTransient<EmployeeDetailsViewModel>();
            services.AddTransient<EmployeeFormViewModel>();
            services.AddTransient<JobTitlesViewModel>();
            services.AddTransient<SalaryViewModel>();
            services.AddTransient<FinancialTransactionViewModel>();
            services.AddTransient<DepartmentViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<BackupViewModel>();
            services.AddTransient<AuditViewModel>();
            services.AddTransient<SettingsViewModel>();

            // Views
            services.AddTransient<MainWindow>();
            services.AddTransient<DashboardView>();
            services.AddTransient<JobTitlesView>();
            services.AddTransient<ReportsView>();
            services.AddTransient<EmployeeListView>();
            services.AddTransient<EmployeeDetailsView>();
            services.AddTransient<SalaryView>();
            services.AddTransient<FinancialTransactionView>();
            services.AddTransient<DepartmentView>();
            services.AddTransient<BackupView>();
            services.AddTransient<AuditView>();
            services.AddTransient<SettingsView>();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            Console.WriteLine("🚀 Starting HR System Application...");

            try
            {
                Console.WriteLine("✅ Creating service provider...");

                // إنشاء النافذة الرئيسية مباشرة
                Console.WriteLine("✅ Creating main window...");

                var mainWindow = new Window
                {
                    Title = "نظام إدارة الموظفين - HR System",
                    Width = 1200,
                    Height = 800,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    WindowState = WindowState.Normal,
                    ShowInTaskbar = true,
                    ResizeMode = ResizeMode.CanResize,
                    Content = new TextBlock
                    {
                        Text = "🎉 نظام إدارة الموظفين 🎉\n\nHR System\n\nالتطبيق جاهز للاستخدام\nApplication is ready to use\n\nجاري تحميل البيانات...\nLoading data...",
                        FontSize = 24,
                        FontWeight = FontWeights.Bold,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        TextAlignment = TextAlignment.Center,
                        Foreground = Brushes.DarkBlue,
                        Background = Brushes.LightBlue,
                        Padding = new Thickness(20)
                    }
                };

                // تعيين النافذة الرئيسية
                this.MainWindow = mainWindow;

                Console.WriteLine("✅ Showing main window...");
                mainWindow.Show();
                mainWindow.Activate();
                mainWindow.Focus();

                Console.WriteLine($"Main Window Visible: {mainWindow.IsVisible}");
                Console.WriteLine($"Main Window Loaded: {mainWindow.IsLoaded}");

                Console.WriteLine("✅ Main window setup completed");

                // تهيئة البيانات في الخلفية (اختياري)
                Task.Run(async () => {
                    try
                    {
                        Console.WriteLine("🔄 Initializing data...");
                        await InitializeDatabase();
                        Console.WriteLine("✅ Data initialization completed");

                        // تحديث النافذة لإظهار أن البيانات جاهزة
                        Dispatcher.Invoke(() => {
                            if (mainWindow.Content is TextBlock textBlock)
                            {
                                textBlock.Text = "🎉 نظام إدارة الموظفين 🎉\n\nHR System\n\nالتطبيق جاهز للاستخدام\nApplication is ready to use\n\n✅ تم تحميل البيانات بنجاح\nData loaded successfully";
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ Data initialization error: {ex.Message}");
                        Dispatcher.Invoke(() => {
                            if (mainWindow.Content is TextBlock textBlock)
                            {
                                textBlock.Text = "🎉 نظام إدارة الموظفين 🎉\n\nHR System\n\nالتطبيق جاهز للاستخدام\nApplication is ready to use\n\n⚠️ تحذير: مشكلة في تحميل البيانات\nWarning: Data loading issue";
                            }
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Critical error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                               "خطأ خطير", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Shutdown();
            }
        }

        private async Task InitializeDatabase()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<HRSystemContext>();

                // تأكد من إنشاء قاعدة البيانات
                await context.Database.EnsureCreatedAsync();

                Console.WriteLine("✅ Database initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Database initialization error: {ex.Message}");
                throw;
            }
        }

        private async Task EnsureAdminUserExists()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                var adminUser = await userService.GetUserByUsernameAsync("admin");
                if (adminUser == null)
                {
                    var admin = new HRSystem.Core.Models.User
                    {
                        Username = "admin",
                        PasswordHash = "", // سيتم تعيينه في CreateUserAsync
                        Email = "<EMAIL>",
                        Role = "Admin",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        LastLogin = DateTime.Now
                    };

                    await userService.CreateUserAsync(admin, "admin");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Admin user creation error: {ex.Message}");
            }
        }

        private async Task SeedInitialData()
        {
            try
            {
                var dataSeedingService = _serviceProvider.GetRequiredService<DataSeedingService>();
                await dataSeedingService.SeedDataAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Data seeding error: {ex.Message}");
            }
        }
    }
}

