﻿using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using HRSystem.Core.Interfaces;
using HRSystem.Core.Services;
using HRSystem.Data.Context;
using HRSystem.Data.Repositories;
using HRSystem.WPF.ViewModels;
using HRSystem.WPF.Views;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace HRSystem.WPF
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider _serviceProvider;

        public ServiceProvider ServiceProvider => _serviceProvider;

        public App()
        {
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();
        }

        private void ConfigureServices(IServiceCollection services)
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "HRSystem.db");
            
            // DbContext
            services.AddDbContext<HRSystemContext>(options =>
                options.UseSqlite($"Data Source={dbPath}"));
            services.AddScoped<IHRSystemContext>(provider => provider.GetRequiredService<HRSystemContext>());

            // Repositories
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

            // Services
            services.AddScoped<EmployeeService>();
            services.AddScoped<SalaryService>();
            services.AddScoped<UserService>();
            services.AddScoped<NotificationService>();
            services.AddScoped<DataSeedingService>();
            services.AddScoped<IExportService, ExportService>();
            services.AddScoped<IBackupService, BackupService>();
            services.AddScoped<IAuditService, AuditService>();
            services.AddScoped<ISettingsService, SettingsService>();
            services.AddScoped<ISecurityService, SecurityService>();

            // ViewModels
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<EmployeeListViewModel>();

            services.AddTransient<EmployeeDetailsViewModel>();
            services.AddTransient<EmployeeFormViewModel>();
            services.AddTransient<JobTitlesViewModel>();
            services.AddTransient<SalaryViewModel>();
            services.AddTransient<FinancialTransactionViewModel>();
            services.AddTransient<DepartmentViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<BackupViewModel>();
            services.AddTransient<AuditViewModel>();
            services.AddTransient<SettingsViewModel>();

            // Views
            services.AddTransient<MainWindow>();
            services.AddTransient<DashboardView>();
            services.AddTransient<JobTitlesView>();
            services.AddTransient<ReportsView>();
            services.AddTransient<EmployeeListView>();
            services.AddTransient<EmployeeDetailsView>();
            services.AddTransient<SalaryView>();
            services.AddTransient<FinancialTransactionView>();
            services.AddTransient<DepartmentView>();
            services.AddTransient<BackupView>();
            services.AddTransient<AuditView>();
            services.AddTransient<SettingsView>();
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            Console.WriteLine("🚀 Starting HR System Application...");

            try
            {
                Console.WriteLine("✅ Creating service provider...");

                // إنشاء نافذة بسيطة للاختبار أولاً
                Console.WriteLine("✅ Creating test window...");
                var testWindow = new Window
                {
                    Title = "🔥 اختبار النافذة - Window Test",
                    Width = 600,
                    Height = 400,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    WindowState = WindowState.Normal,
                    ShowInTaskbar = true,
                    Topmost = true,
                    Content = new System.Windows.Controls.TextBlock
                    {
                        Text = "✅ النافذة تعمل بنجاح!\n\nWindow is working successfully!\n\nClick OK to continue to main application.",
                        FontSize = 18,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        TextAlignment = TextAlignment.Center
                    }
                };

                Console.WriteLine("✅ Showing test window...");
                testWindow.Show();
                testWindow.Activate();
                testWindow.Focus();

                Console.WriteLine($"Test Window Visible: {testWindow.IsVisible}");
                Console.WriteLine($"Test Window Loaded: {testWindow.IsLoaded}");

                // إظهار رسالة تأكيد
                Console.WriteLine("✅ Showing confirmation dialog...");
                var result = MessageBox.Show("هل تريد المتابعة إلى التطبيق الرئيسي؟\nDo you want to continue to main application?",
                                           "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                Console.WriteLine($"User choice: {result}");
                testWindow.Close();

                if (result == MessageBoxResult.Yes)
                {
                    Console.WriteLine("✅ Creating simple main window...");

                    // إنشاء نافذة بسيطة مباشرة
                    var simpleWindow = new Window
                    {
                        Title = "نظام إدارة الموظفين - HR System",
                        Width = 1200,
                        Height = 800,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen,
                        WindowState = WindowState.Normal,
                        ShowInTaskbar = true,
                        Topmost = true,
                        ResizeMode = ResizeMode.CanResize,
                        Content = new TextBlock
                        {
                            Text = "🎉 نظام إدارة الموظفين يعمل بنجاح! 🎉\n\nHR System is working successfully!\n\nالتطبيق جاهز للاستخدام\nApplication is ready to use",
                            FontSize = 28,
                            FontWeight = FontWeights.Bold,
                            HorizontalAlignment = HorizontalAlignment.Center,
                            VerticalAlignment = VerticalAlignment.Center,
                            TextAlignment = TextAlignment.Center,
                            Foreground = Brushes.DarkBlue,
                            Background = Brushes.LightBlue,
                            Padding = new Thickness(20)
                        }
                    };

                    // تعيين النافذة الرئيسية
                    this.MainWindow = simpleWindow;

                    Console.WriteLine("✅ Showing simple window...");
                    simpleWindow.Show();
                    simpleWindow.Activate();
                    simpleWindow.Focus();
                    simpleWindow.BringIntoView();

                    // محاولة إضافية لإظهار النافذة
                    simpleWindow.WindowState = WindowState.Maximized;
                    System.Threading.Thread.Sleep(100);
                    simpleWindow.WindowState = WindowState.Normal;

                    Console.WriteLine($"Simple Window Visible: {simpleWindow.IsVisible}");
                    Console.WriteLine($"Simple Window Loaded: {simpleWindow.IsLoaded}");

                    Console.WriteLine("✅ Simple window setup completed");

                    // تهيئة البيانات في الخلفية
                    Task.Run(async () => {
                        try
                        {
                            Console.WriteLine("🔄 Initializing data...");
                            await EnsureAdminUserExists();
                            await SeedInitialData();
                            Console.WriteLine("✅ Data initialization completed");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"❌ Data initialization error: {ex.Message}");
                            Dispatcher.Invoke(() => {
                                MessageBox.Show($"تحذير: خطأ في تهيئة البيانات: {ex.Message}", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                            });
                        }
                    });
                }
                else
                {
                    Console.WriteLine("❌ User cancelled - shutting down");
                    this.Shutdown();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Critical error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                MessageBox.Show($"خطأ في بدء التطبيق: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                               "خطأ خطير", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Shutdown();
            }
        }

        private async Task EnsureAdminUserExists()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();

                var adminUser = await userService.GetUserByUsernameAsync("admin");
                if (adminUser == null)
                {
                    var admin = new HRSystem.Core.Models.User
                    {
                        Username = "admin",
                        PasswordHash = "", // سيتم تعيينه في CreateUserAsync
                        Email = "<EMAIL>",
                        Role = "Admin",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        LastLogin = DateTime.Now
                    };

                    await userService.CreateUserAsync(admin, "admin");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء المستخدم الافتراضي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task SeedInitialData()
        {
            try
            {
                var dataSeedingService = _serviceProvider.GetRequiredService<DataSeedingService>();
                await dataSeedingService.SeedDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة البيانات الأساسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}

