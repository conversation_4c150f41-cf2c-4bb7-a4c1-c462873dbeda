﻿using System.Windows;
using System.Windows.Controls;
using HRSystem.WPF.Views;
using Microsoft.Extensions.DependencyInjection;
using HRSystem.WPF.ViewModels;

namespace HRSystem.WPF;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly IServiceProvider _serviceProvider;

    public MainWindow(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        InitializeComponent();

        // فرض ظهور النافذة جبرياً
        this.WindowState = WindowState.Normal;
        this.ShowInTaskbar = true;
        this.Topmost = true;

        // تحميل لوحة التحكم افتراضياً
        LoadDashboard();

        // تأكد من ظهور النافذة
        this.Loaded += MainWindow_Loaded;
        this.Activated += MainWindow_Activated;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // فرض ظهور النافذة
        this.WindowState = WindowState.Maximized;
        this.Activate();
        this.Focus();
        this.BringIntoView();

        // تسجيل في سجل Windows
        System.Diagnostics.Debug.WriteLine("MainWindow loaded successfully");

        // إظهار رسالة للتأكد من أن النافذة تعمل
        MessageBox.Show("النافذة الرئيسية تم تحميلها بنجاح!\nMainWindow loaded successfully!",
                       "نجح التحميل", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void MainWindow_Activated(object sender, EventArgs e)
    {
        System.Diagnostics.Debug.WriteLine("MainWindow activated");
    }

    private void LoadDashboard()
    {
        MessageBox.Show("تم النقر على لوحة التحكم", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        LoadDashboard();
    }

    private void EmployeesButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على الموظفين", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void DepartmentsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على الأقسام", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void JobTitlesButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على المناصب", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SalariesButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على الرواتب", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void TransactionsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على المعاملات المالية", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void ReportsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على التقارير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BackupButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على النسخ الاحتياطي", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void AuditButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على سجل المراجعة", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("تم النقر على الإعدادات", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
    }
}